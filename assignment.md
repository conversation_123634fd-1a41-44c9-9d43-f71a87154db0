Senior Go Engineer / Home
Assignment
Overview
Go engineers take an aesthetic approach to coding. For this home assignment, we
are looking to measure your design skills, both visible and invisible.
Please review the task below. Once done, send your completed work as a git
repository that requires the least friction to get up and running.
We value clean, maintainable code that follows Go best practices and
demonstrates strong understanding of concurrent programming patterns. Your
solution should strike a balance between performance and code readability.
⚠️ Please ensure your code meets production standards, including proper error
handling, logging, and documentation that would make it suitable for deployment
in a real-world environment.
Task Description
You are given a computationally expensive or long-running function. Your task is
to implement caching of its results with the following requirements:
Functional Requirements
1. Memoization
Cache the results of the function to avoid redundant computations for the
same input parameters.
2. In-flight Request Deduplication
If multiple requests with the same parameters are made while the function is
still running, only the first call should execute the function.
The rest should wait for the result of the first one.
This is to reduce resource usage and avoid duplicate traffic.
Senior Go Engineer / Home Assignment 1
3. Expiration
Cached results should expire after 5 minutes (TTL of 5 minutes per entry).
4. Capacity Limit
The cache should store no more than 1000 entries.
When the cache exceeds this limit, the oldest entries should be evicted (LRU
policy preferred).
Technical Requirements
Write in Go.
Your implementation should be concurrent-safe.
You may use any publicly available open-source packages except fullfeatured caching libraries like:
bigcache
groupcache
ristretto
You may use standard library synchronization primitives
( sync.Mutex ,  sync.Map ,  sync.Cond , etc.).
API Usage Example
Here's how the caching layer should be used in practice:
// A long-running function that fetches data (simulated with sleep)
func fetchDataFromRemote(id int) (string, error) {
 time.Sleep(2 * time.Second)
 return fmt.Sprintf("Result for ID %d", id), nil
}
func main() {
 // Wrap the function with your caching layer
 cachedFetch := NewCachedFunction(fetchDataFromRemote)
Senior Go Engineer / Home Assignment 2
 // Call the function
 result, err := cachedFetch(42)
 if err != nil {
 log.Fatal(err)
 }
 fmt.Println(result) // Output: Result for ID 42
 // This second call will return instantly from the cache
 result, err = cachedFetch(42)
 fmt.Println(result) // Output: Result for ID 42 (cached)
}
Example of concurrent deduplication:
var wg sync.WaitGroup
cachedFetch := NewCachedFunction(fetchDataFromRemote)
for i := 0; i < 10; i++ {
 wg.Add(1)
 go func() {
 defer wg.Done()
 result, err := cachedFetch(100)
 if err == nil {
 fmt.Println("Got:", result)
 }
 }()
}
wg.Wait()
// Only one fetchDataFromRemote should actually be called.
Senior Go Engineer / Home Assignment 3
Testing Requirements
Write unit tests for the following:
Return values are correctly cached
Results expire after 5 minutes
Concurrent calls with same input are deduplicated
Cache never exceeds 1000 entries
Oldest entries are evicted when the cache is full
Optional Bonus
Provide a benchmark comparing:
Direct function execution
Cached execution (cold/warm)
Performance under high concurrency
Senior Go Engineer / Home Assignment 4